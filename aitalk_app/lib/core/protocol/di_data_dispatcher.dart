import 'dart:async';
import 'dart:typed_data';

import 'at_response_handler.dart';
import 'binary_escape.dart';
import 'tk8620_frame_decoder.dart';
import 'text_packet_assembler.dart';
import 'voice_packet_assembler.dart';
import 'tk8620_protocol.dart';
import 'package:flutter/foundation.dart'; // debugPrint
import '../audio/voice_wav_saver.dart';
import '../audio/voice_player.dart';
import '../audio/multi_frame_voice_assembler.dart';
import '../audio/call_audio_session_manager.dart';
import '../services/conversation_manager.dart';
import 'join_response_processor.dart';
import 'join_notification_handler.dart';
import '../services/database_service.dart'; // 新增导入
import '../bluetooth/bluetooth_manager.dart'; // 修正导入
import '../utils/group_util.dart'; // 新增导入
import '../utils/encryption_utils.dart'; // 加密工具
import 'tk8620_request_sender.dart'; // 修正导入
import 'tk8620_protocol.dart'; // TK8620SessionIdGenerator 定义于此
import 'session_join_handler.dart'; // 新增导入
import 'voice_call_handler.dart'; // 新增导入
// 不再单独创建解码器，直接复用 VoicePlayer.playFrame 的返回结果

/// DI 数据统一分发器
///
/// 职责:
///   1. 监听 `AtResponseHandler` 的广播流;
///   2. 仅处理 `AtResponseType.diData` 响应, 提取未转义字节;
///   3. 进行 TK8620 帧解码, 解码成功后通过 `frames` Stream 广播;
///
///   *不* 关心后续业务载荷解析, 上层可使用 `TK8620PayloadParser` 自行处理。
class DiDataDispatcher {
  DiDataDispatcher._() {
    // 初始化远端语音播放器
    VoicePlayer.create().then((vp) => _voicePlayer = vp);
    // 自动播放设置将在main.dart中加载并通过setAutoPlayVoice()方法设置
    _sub = AtResponseHandler.instance.responses.listen(_onAtResponse);
  }

  static final DiDataDispatcher instance = DiDataDispatcher._();

  late final StreamSubscription<AtResponse> _sub;

  final StreamController<TK8620Frame> _frameController =
      StreamController<TK8620Frame>.broadcast();

  // 解析后的业务对象流
  final StreamController<TK8620Message> _messageController =
      StreamController<TK8620Message>.broadcast();

  // 文本包组装器
  final TextPacketAssembler _textAssembler = TextPacketAssembler();
  // 语音包组装器
  final VoicePacketAssembler _voiceAssembler = VoicePacketAssembler();

  // 语音播放器实例（收到语音帧时即时解码播放）
  VoicePlayer? _voicePlayer;

  // 是否自动播放远端语音，默认开启
  bool _autoPlayVoice = true;

  /// 供外部(设置页)调用，以实时更新偏好
  void setAutoPlayVoice(bool enabled) {
    _autoPlayVoice = enabled;
  }

  // 👉 PCM 缓存/保存逻辑已移至 VoiceWavSaver

  /// 已解析出的 TK8620 帧流
  Stream<TK8620Frame> get frames => _frameController.stream;

  /// 解析后的业务消息流 (高层直接使用此流，无需关注底层协议细节)
  Stream<TK8620Message> get messages => _messageController.stream;

  void _onAtResponse(AtResponse res) {
    if (res.type != AtResponseType.diData) return;

    final raw = res.payload?['raw'];
    if (raw is! Uint8List || raw.isEmpty) {
      debugPrint('[DiDataDispatcher] 无效的原始数据');
      return;
    }

    // 此处 raw 已经是 BinaryEscape.unescape 的结果, 但为了稳妥再确保一次
    final Uint8List unescaped = raw;
    debugPrint('[DiDataDispatcher] 收到DI数据，长度: ${unescaped.length}');
    debugPrint(
      '[DiDataDispatcher] 原始数据: ${unescaped.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
    );

    final frame = TK8620FrameDecoder.tryDecode(unescaped);
    if (frame != null) {
      debugPrint(
        '[DiDataDispatcher] 成功解码TK8620帧: ${frame.frameType}, src=${frame.srcId}, dst=0x${frame.dstId.toRadixString(16).padLeft(8, '0')}',
      );
      debugPrint(
        '[DiDataDispatcher] 载荷长度: ${frame.payload.length}, 载荷内容: ${frame.payload.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );
      _frameController.add(frame);

      // 进一步解析帧载荷并分发业务消息
      _handleFrame(frame);
    } else {
      debugPrint('[DiDataDispatcher] TK8620帧解码失败');
    }
  }

  /// 根据帧类型解析载荷并生成业务消息
  void _handleFrame(TK8620Frame frame) {
    if (frame.payload.isEmpty) return;

    dynamic parsed;

    switch (frame.frameType) {
      case TK8620FrameType.session:
        final ctrl = frame.payload[0];
        switch (ctrl) {
          case TK8620SessionCode.joinRequest:
            parsed = TK8620PayloadParser.parseJoinRequest(frame.payload);
            if (parsed is TK8620SessionJoinRequest) {
              // 异步处理密码校验与响应
              unawaited(SessionJoinHandler.handle(frame, parsed));
            }
            break;
          case TK8620SessionCode.joinResponse:
            parsed = TK8620PayloadParser.parseJoinResponse(frame.payload);

            if (parsed is TK8620SessionJoinResponse && parsed.isSuccess) {
              // 取 groupId (即 SessionID 的8位16进制形式)
              final String groupId = parsed.sessionId
                  .toRadixString(16)
                  .padLeft(8, '0')
                  .toUpperCase();

              // 获取缓存的密码并传递给处理器
              final joinPassword = JoinPasswordCache.getAndClearPassword();
              unawaited(
                JoinResponseProcessor.handle(
                  groupId,
                  parsed,
                  joinPassword: joinPassword,
                ),
              );
            }
            break;
          case TK8620SessionCode.createTalkRequest:
            parsed = TK8620PayloadParser.parseCreateTalkRequest(frame.payload);
            if (parsed is TK8620CreateTalkRequest) {
              // 处理建立通话请求
              debugPrint('📞 收到建立通话请求: ${parsed.toString()}');
              unawaited(
                VoiceCallHandler.handleCreateTalkRequest(frame, parsed),
              );
            }
            break;
          case TK8620SessionCode.createTalkResponse:
            parsed = TK8620PayloadParser.parseCreateTalkResponse(frame.payload);
            if (parsed is TK8620CreateTalkResponse) {
              // 处理建立通话响应
              debugPrint('📞 收到建立通话响应: ${parsed.toString()}');
              unawaited(
                VoiceCallHandler.handleCreateTalkResponse(frame, parsed),
              );
            }
            break;
          case TK8620SessionCode.joinNotify:
            parsed = TK8620PayloadParser.parseJoinNotification(
              frame.payload,
              frame.srcId,
              frame.dstId,
            );
            if (parsed is TK8620JoinNotification) {
              // 处理加入会话通知
              debugPrint('👥 收到加入会话通知: ${parsed.toString()}');
              unawaited(JoinNotificationHandler.handle(parsed));
            }
            break;
          // 其他 SessionCode 可在此扩展

          default:
            // ⚠️ 未识别的会话控制码
            debugPrint(
              '⚠️ 未识别的 Session 控制码: 0x${ctrl.toRadixString(16).padLeft(2, '0')}',
            );
            break;
        }
        break;

      case TK8620FrameType.data:
        final dataType = frame.payload[0];
        switch (dataType) {
          case TK8620DataType.text:
            // 文本消息需要异步解密处理
            _handleTextFrameAsync(frame);
            return; // 异步处理，不在这里继续
          case TK8620DataType.gps:
            parsed = TK8620PayloadParser.parseGPSData(frame.payload);
            break;
          case TK8620DataType.voice:
            // 语音消息需要异步解密处理
            _handleVoiceFrameAsync(frame);
            return; // 异步处理，不在这里继续

          default:
            // ⚠️ 未识别的数据类型码
            debugPrint(
              '⚠️ 未识别的数据类型码: 0x${dataType.toRadixString(16).padLeft(2, '0')}',
            );
            break;
        }
        break;

      case TK8620FrameType.voice:
        // 语音帧需要异步解密处理
        _handleVoiceFrameAsync(frame);

        // 语音播放：区分PTT语音和实时通话语音
        if (frame.communicationMode == TK8620CommunicationMode.realTime) {
          // 实时通话语音
          _handleRealTimeVoiceAsync(frame);
        } else {
          // PTT语音
          _handleVoicePlaybackAsync(frame);
        }
        return; // 异步处理，不在这里继续

      case TK8620FrameType.command:
        // TODO: 根据具体命令码解析

        // 如果未实现，打印提示，方便后续补充
        debugPrint('⚠️ 未实现的命令帧解析');
        break;

      default:
        // 理论上不会走到此分支，防御性代码
        debugPrint('⚠️ 未识别的帧类型: ${frame.frameType}');
        break;
    }

    if (parsed != null) {
      debugPrint('[DiDataDispatcher] 生成业务消息: ${parsed.runtimeType}');
      _messageController.add(TK8620Message(frame: frame, payload: parsed));
    } else {
      debugPrint('[DiDataDispatcher] 载荷解析失败，未生成业务消息');
    }

    // 👂 语音播放：区分PTT语音和实时通话语音
    if (frame.frameType == TK8620FrameType.voice ||
        (frame.frameType == TK8620FrameType.data &&
            frame.payload.isNotEmpty &&
            frame.payload[0] == TK8620DataType.voice)) {
      // PTT语音：异步处理解密
      _handleVoicePlaybackAsync(frame);
    } else if (frame.frameType == TK8620FrameType.voice &&
        frame.communicationMode == TK8620CommunicationMode.realTime) {
      // 实时通话语音：异步处理解密
      _handleRealTimeVoiceAsync(frame);
    }
  }

  /// 检测是否为多帧数据包
  ///
  /// 多帧数据包的特征：
  /// 1. 第一个字节是帧数量（1-20，放宽限制以支持更多帧数）
  /// 2. 后续数据长度应该等于 帧数量 * 11字节
  bool _isMultiFramePacket(Uint8List audioData) {
    if (audioData.isEmpty) return false;

    // 检查第一个字节是否为合理的帧数量
    final frameCount = audioData[0];
    // 使用MultiFrameVoiceAssembler定义的最大帧数限制
    if (frameCount < 1 ||
        frameCount > MultiFrameVoiceAssembler.maxFramesPerPacket) {
      debugPrint(
        '🔍 帧数量超出范围: $frameCount (最大${MultiFrameVoiceAssembler.maxFramesPerPacket})，判定为单帧',
      );
      return false;
    }

    // 检查数据长度是否匹配多帧格式
    final expectedLength =
        1 + frameCount * MultiFrameVoiceAssembler.melpFrameSize;
    if (audioData.length != expectedLength) {
      debugPrint('🔍 长度不匹配: 期望$expectedLength，实际${audioData.length}，判定为单帧');
      return false;
    }

    // 如果数据长度正好等于单个MELP帧的大小，则认为是单帧
    if (audioData.length == MultiFrameVoiceAssembler.melpFrameSize) {
      debugPrint(
        '🔍 长度等于单帧大小(${MultiFrameVoiceAssembler.melpFrameSize})，判定为单帧',
      );
      return false;
    }

    debugPrint('🔍 检测到多帧数据包: $frameCount帧，总长度${audioData.length}字节');
    return true;
  }

  /// 异步处理文本帧（支持解密）
  Future<void> _handleTextFrameAsync(TK8620Frame frame) async {
    try {
      final textData = await _textAssembler.handleTextFrame(frame);
      if (textData != null) {
        // 构造消息对象并广播
        final message = TK8620Message(frame: frame, payload: textData);
        _messageController.add(message);
        debugPrint('📤 广播文本消息: ${textData.text}');
      }
    } catch (e) {
      debugPrint('❌ 异步处理文本帧失败: $e');
    }
  }

  /// 异步处理语音帧（支持解密）
  Future<void> _handleVoiceFrameAsync(TK8620Frame frame) async {
    try {
      final voiceData = await _voiceAssembler.handleVoiceFrame(frame);
      if (voiceData != null) {
        // 构造消息对象并广播
        final message = TK8620Message(frame: frame, payload: voiceData);
        _messageController.add(message);
        debugPrint('📤 广播语音消息: ${voiceData.audioData.length}字节');
      }
    } catch (e) {
      debugPrint('❌ 异步处理语音帧失败: $e');
    }
  }

  /// 异步处理语音播放（支持解密）
  Future<void> _handleVoicePlaybackAsync(TK8620Frame frame) async {
    try {
      debugPrint('🎵 [DiDataDispatcher] 开始处理语音播放...');
      final groupId = ConversationManager.currentConversationId.value;
      debugPrint('🎵 [PTT语音] 群组ID: "$groupId"');

      final vd = await TK8620PayloadParser.parseVoiceData(
        frame.payload,
        groupId: groupId,
      );
      if (vd != null) {
        // 检测是否为多帧数据包（通过数据长度和格式判断）
        if (_isMultiFramePacket(vd.audioData)) {
          // 处理多帧数据包
          // playMultiFramePacket方法会自己处理帧数量字节，不需要提前去掉
          _voicePlayer
              ?.playMultiFramePacket(vd.audioData, playAudio: _autoPlayVoice)
              .then((List<Int16List> pcmFrames) async {
                // 将多帧PCM数据逐帧保存
                for (int i = 0; i < pcmFrames.length; i++) {
                  final isLastFrame =
                      vd.isLastPacket && (i == pcmFrames.length - 1);
                  await VoiceWavSaver.instance.handleVoice(
                    srcId: frame.srcId,
                    pcm: pcmFrames[i],
                    isLastPacket: isLastFrame,
                    isMine: false,
                  );
                }
              });
        } else {
          // 处理单帧数据包（兼容旧格式）
          _voicePlayer?.playFrame(vd.audioData, playAudio: _autoPlayVoice).then(
            (Int16List? pcm) async {
              if (pcm != null) {
                await VoiceWavSaver.instance.handleVoice(
                  srcId: frame.srcId,
                  pcm: pcm,
                  isLastPacket: vd.isLastPacket,
                  isMine: false,
                );
              }
            },
          );
        }
      }
    } catch (e) {
      debugPrint('❌ 异步处理语音播放失败: $e');
    }
  }

  /// 异步处理实时通话语音（支持解密）
  Future<void> _handleRealTimeVoiceAsync(TK8620Frame frame) async {
    try {
      // 实时通话语音使用当前激活的群组ID进行解密
      final groupId = ConversationManager.currentConversationId.value;
      debugPrint('📞 [实时通话] 开始处理语音，群组ID: "$groupId"');

      // 如果群组ID为空，直接返回，不播放
      if (groupId == null || groupId.isEmpty) {
        debugPrint('❌ [实时通话] 群组ID为空，跳过语音播放');
        return;
      }

      final vd = await TK8620PayloadParser.parseVoiceData(
        frame.payload,
        groupId: groupId,
      );
      if (vd != null) {
        debugPrint('🔊 收到实时通话语音: ${vd.audioData.length}字节');

        // 确保在通话模式下播放（听筒模式，避免外放回响）
        if (CallAudioSessionManager.instance.isCallModeActive) {
          debugPrint('🎧 实时通话模式下播放语音（听筒模式）');

          // 解密后的数据第一个字节是帧数量，需要去掉
          if (vd.audioData.isNotEmpty) {
            final frameCount = vd.audioData[0];
            final melpData = vd.audioData.sublist(1);
            debugPrint('🎵 实时语音帧数: $frameCount, MELP数据: ${melpData.length}字节');
            _voicePlayer?.playMultiFramePacket(
              melpData,
              playAudio: _autoPlayVoice,
            );
          }
        } else {
          debugPrint('⚠️ 收到实时通话语音但未处于通话模式，跳过播放');
        }
        // 注意：实时通话语音不保存为WAV文件
      }
    } catch (e) {
      debugPrint('❌ 异步处理实时通话语音失败: $e');
    }
  }

  // _handleSuccessfulJoin 移至 JoinResponseProcessor
}

/// 封装了原始帧及解析后载荷的业务消息对象
class TK8620Message {
  TK8620Message({required this.frame, required this.payload});

  final TK8620Frame frame;
  final dynamic payload; // 具体类型取决于帧内容, 例如 TK8620TextData / TK8620GPSData 等

  @override
  String toString() => 'TK8620Message(${frame.frameType}, payload: $payload)';
}
